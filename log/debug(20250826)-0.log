2025-08-26 16:00:06.546 [DEBUG] @main StartupInfoLogger.logStarting #56: Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-26 16:00:44.231 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[127.0.0.1]
2025-08-26 16:00:44.231 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[**************]
2025-08-26 16:00:44.232 [DEBUG] @main IdWorker.getInstance #73: 设置dataCenterId[6]和workerId[31]
2025-08-26 16:02:23.071 [DEBUG] @main StartupInfoLogger.logStarting #56: Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-26 16:03:05.893 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[127.0.0.1]
2025-08-26 16:03:05.893 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[**************]
2025-08-26 16:03:05.893 [DEBUG] @main IdWorker.getInstance #73: 设置dataCenterId[6]和workerId[31]
2025-08-26 16:03:21.600 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedJetty类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.602 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.607 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.http.JsonbHttpMessageConvertersConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.612 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.transaction.jta.AtomikosJtaConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.614 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.646 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.648 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.InfinispanCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.649 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.sql.init.R2dbcInitializationConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.651 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.JCacheCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.669 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.673 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.data.redis.JedisConnectionConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.675 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jms.artemis.ArtemisAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.680 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.data.rest.RepositoryRestMvcAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.700 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.701 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.Cache2kCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.714 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.EhCacheCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.719 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.HazelcastCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.721 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:03:21.733 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.CouchbaseCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$26a6f7c2.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$57d32a18.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:14:14.639 [DEBUG] @pool-5-thread-1 Result.setCode #82: 设置message11002失败
org.springframework.context.NoSuchMessageException: No message found under code '11002' for locale 'zh_CN'.
	at org.springframework.context.support.AbstractMessageSource.getMessage(AbstractMessageSource.java:161)
	at com.chinamobile.sparrow.domain.util.I18NUtil.getMessage(I18NUtil.java:21)
	at com.chinamobile.sparrow.domain.infra.code.Result.setCode(Result.java:78)
	at com.chinamobile.sparrow.domain.infra.code.Result.setCode(Result.java:67)
	at com.chinamobile.sparrow.domain.service.iot.infra.ApiBase.invoke(ApiBase.java:44)
	at com.chinamobile.sparrow.domain.service.iot.DataFacade.lambda$monthlyDataUsage$5(DataFacade.java:112)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-26 16:14:51.719 [DEBUG] @pool-5-thread-2 Result.setCode #82: 设置message11006失败
org.springframework.context.NoSuchMessageException: No message found under code '11006' for locale 'zh_CN'.
	at org.springframework.context.support.AbstractMessageSource.getMessage(AbstractMessageSource.java:161)
	at com.chinamobile.sparrow.domain.util.I18NUtil.getMessage(I18NUtil.java:21)
	at com.chinamobile.sparrow.domain.infra.code.Result.setCode(Result.java:78)
	at com.chinamobile.sparrow.domain.infra.code.Result.setCode(Result.java:67)
	at com.chinamobile.sparrow.domain.service.iot.infra.ApiBase.invoke(ApiBase.java:44)
	at com.chinamobile.sparrow.domain.service.iot.DataFacade.lambda$monthlyDataUsage$5(DataFacade.java:112)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-26 16:16:06.293 [DEBUG] @pool-5-thread-3 Result.setCode #82: 设置message11006失败
org.springframework.context.NoSuchMessageException: No message found under code '11006' for locale 'zh_CN'.
	at org.springframework.context.support.AbstractMessageSource.getMessage(AbstractMessageSource.java:161)
	at com.chinamobile.sparrow.domain.util.I18NUtil.getMessage(I18NUtil.java:21)
	at com.chinamobile.sparrow.domain.infra.code.Result.setCode(Result.java:78)
	at com.chinamobile.sparrow.domain.infra.code.Result.setCode(Result.java:67)
	at com.chinamobile.sparrow.domain.service.iot.infra.ApiBase.invoke(ApiBase.java:44)
	at com.chinamobile.sparrow.domain.service.iot.DataFacade.lambda$dailyDataUsage$2(DataFacade.java:58)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at java.lang.Thread.run(Thread.java:750)
2025-08-26 16:29:34.552 [DEBUG] @main StartupInfoLogger.logStarting #56: Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-26 16:30:01.519 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[127.0.0.1]
2025-08-26 16:30:01.519 [DEBUG] @main IdWorker.getInstance #61: 获取到IPv4地址[**************]
2025-08-26 16:30:01.519 [DEBUG] @main IdWorker.getInstance #73: 设置dataCenterId[6]和workerId[31]
2025-08-26 16:30:13.665 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.data.redis.JedisConnectionConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.667 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.data.rest.RepositoryRestMvcAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.682 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.HazelcastCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.686 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.InfinispanCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.689 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jms.artemis.ArtemisAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.691 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.CouchbaseCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.704 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.sql.init.R2dbcInitializationConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.709 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.liquibase.LiquibaseAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.724 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedUndertow类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.740 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.EhCacheCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.743 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.758 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.Cache2kCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.766 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.767 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.jsonb.JsonbAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.772 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedJetty类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.774 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.783 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.transaction.jta.AtomikosJtaConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.783 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.cache.JCacheCacheConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)
2025-08-26 16:30:13.793 [DEBUG] @main PermissionRepository.lambda$readControllerPathMappings$16 #298: 读取Controller/RequestMapping注解类，无法解析org.springframework.boot.autoconfigure.http.JsonbHttpMessageConvertersConfiguration类
java.lang.ArrayStoreException: sun.reflect.annotation.TypeNotPresentExceptionProxy
	at sun.reflect.annotation.AnnotationParser.parseClassArray(AnnotationParser.java:724)
	at sun.reflect.annotation.AnnotationParser.parseArray(AnnotationParser.java:531)
	at sun.reflect.annotation.AnnotationParser.parseMemberValue(AnnotationParser.java:355)
	at sun.reflect.annotation.AnnotationParser.parseAnnotation2(AnnotationParser.java:286)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations2(AnnotationParser.java:120)
	at sun.reflect.annotation.AnnotationParser.parseAnnotations(AnnotationParser.java:72)
	at java.lang.Class.createAnnotationData(Class.java:3521)
	at java.lang.Class.annotationData(Class.java:3510)
	at java.lang.Class.getAnnotation(Class.java:3415)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.lambda$readControllerPathMappings$16(PermissionRepository.java:289)
	at java.lang.Iterable.forEach(Iterable.java:75)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.readControllerPathMappings(PermissionRepository.java:287)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository.renew(PermissionRepository.java:96)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$e90698ca.renew(<generated>)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$FastClassBySpringCGLIB$$43b8ead4.invoke(<generated>)
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:792)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:388)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:762)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:707)
	at com.chinamobile.sparrow.domain.repository.sec.PermissionRepository$$EnhancerBySpringCGLIB$$1a32cb20.renew(<generated>)
	at com.chinamobile.sparrow.domain.infra.sec.shiro.RenewPermissionApplicationRunner.run(RenewPermissionApplicationRunner.java:22)
	at org.springframework.boot.SpringApplication.callRunner(SpringApplication.java:756)
	at org.springframework.boot.SpringApplication.lambda$callRunners$2(SpringApplication.java:746)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.accept(ForEachOps.java:184)
	at java.util.stream.SortedOps$SizedRefSortingSink.end(SortedOps.java:352)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:483)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:472)
	at java.util.stream.ForEachOps$ForEachOp.evaluateSequential(ForEachOps.java:151)
	at java.util.stream.ForEachOps$ForEachOp$OfRef.evaluateSequential(ForEachOps.java:174)
	at java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
	at java.util.stream.ReferencePipeline.forEach(ReferencePipeline.java:418)
	at org.springframework.boot.SpringApplication.callRunners(SpringApplication.java:744)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at com.chinamobile.si.Application.main(Application.java:27)

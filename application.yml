server:
  port: 8080
  servlet:
    context-path:

  # 错误页
  error:
    path: /error
    include-binding-errors: always
    include-exception: true
    include-message: always
    include-stacktrace: always

spring:
  application:
    name: 侨旅宝

  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration

  datasource:
    main:
      config: shardingsphere.yml
      properties:
        hibernate:
          allow_update_outside_transaction: true
          cache:
            use_query_cache: false
            use_second_level_cache: false
          default_schema:
          dialect: com.chinamobile.sparrow.domain.infra.orm.hibernate.dialect.MySQLInnoDBDialect
          hbm2ddl:
            auto: update
          show_sql: true
      packages: com.chinamobile.sparrow.domain.model, com.chinamobile.si.model

    quartz:
      type: com.zaxxer.hikari.HikariDataSource
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ************************************************************************************************************************************************************
      username: gmccai
      password: LPDY!iLrUd8irpGp
      maximum-pool-size: 2

  gson:
    date-format: yyyy-MM-dd HH:mm:ss
    serialize-nulls: true

  jmx:
    enabled: false

  messages:
    basename: i18n/messages
    encoding: UTF-8

  mvc:
    converters:
      preferred-json-mapper: gson

  pid:
    file: app.pid

  quartz:
    job-store-type: jdbc
    jdbc:
      # initialize-schema: always
      initialize-schema: never
    wait-for-jobs-to-complete-on-shutdown: true

  # redis:
  #  host:
  #  port:
  #  password:
  #  lettuce:
  #    pool:
  #      max-active: 64

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

# 日志
logging:
  config: logback-spring.xml

shiro:
  enabled: true
  annotations:
    enabled: true
  web:
    enabled: true
  session-id-cookie:
    name: qlink-session-id
    http-only: true
    same-site: Strict
    secure: false
  remember-me-cookie:
    name: qlink-remember-me
    max-age: 2592000
    http-only: true
    same-site: Strict
    secure: false
  session:
    timeout: 1800
    validation-interval: 3600
  filter-chain-definition:
    - /sec/login/cancel, user
    - /sec/login/**, anon
    - /error, anon
    - /app/init, anon
    - /app/layout/header, anon
    - /media/read, anon
    - /media/read/force-partial-content, anon
    - /util/cmpassport/app-id, anon
    - /util/validation/jquery, anon

    - /test/**, anon

    # 其它
    - /**, user
  redis-session-dao:
    enabled: false
    key-template: "qlink:shiro:session:%s"

# 切面
aspect:
  log:
    enabled: true
    max-length: 128000

# 文件系统存储
file:
  dir:
    env:
    default:
  extension:
    allowed: txt,doc,docx,xls,xlsx,ppt,pptx,ofd,pdf,avif,bmp,gif,ico,jfif,jpeg,jpg,jxl,pjp,pjpeg,png,svg,svgz,tif,tiff,webp,xbm,asx,avi,m4v,mov,mp4,mpeg,mpg,ogm,ogv,webm,wmv,rar,zip
    forbidden:
  part:
    size: 1048576

# 作业
job:
  packages: com.chinamobile

# okhttp连接池
okhttp:
  connection-pool:
    default:
      keep-alive: 5
      max-idle: 5

# 对象存储
s3:
  enabled: false
  endpoint: https://eos-shanghai-1.cmecloud.cn
  access-key:
  secret-key:
  bucket:
    env: qlink-dev
    default: default

# 安全
sec:
  username:
  password-constraint: ^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$
  captcha: true
  login:
    url:
    max-attempts: 5
  oauth2:
    client-id:
    client-secret:
  rsa:
    default:
      public-key:
      private-key:
  sms:
    code-length: 6
    expires-in: 1
    template:

thread-pool:
  # 默认线程池
  default:
    core: 8
    max: 16

web:
  # 跨域
  cors:
    allow-credentials: true
    allowed-headers:
      - "*"
    allowed-methods:
      - GET
      - POST
      - OPTIONS
    allowed-origins:
      - http://localhost
    exposed-headers:
      - "Content-Disposition"
    max-age: 3600

# 中国移动手机号码认证
cmpassport:
  enabled: false
  base-url: https://token.cmpassport.com:8300
  app-id:
  app-key:
  rsa:
    public-key:
    private-key:

# 移动云MAS
mas:
  enabled: false
  sms:
    base-url: https://*************:28888
    ec-name:
    ap-id:
    secret-key:
    sign:

# 企信通
qxt:
  enabled: false
  base-url: http://*************
  eid:
  userId:
  password:
  encrypt-key:
  port:
    default:

# 高德地图
amap:
  api-key:
  js:
    key:
    code:

# 腾讯地图
qqmap:
  js-key:

# 微信公众平台
wx:
  # 企业微信
  cp:
    enabled: false
    corp-id:
    corp-secret:
    redirect:
  # 小程序
  ma:
    enabled: true
    app-id:
    secret:
    msg-data-format: JSON
    version: develop
  # 粤政易
  yzy:
    enabled: false
    base-url: https://zwwx.gdzwfw.gov.cn
    corp-id:
    corp-secret:
    redirect:

# 物联网卡能力开放平台
iot:
  base-url: https://api.iot.10086.cn/v5
  app-id: C5010200216447200200000
  password: LiMNn3RrG
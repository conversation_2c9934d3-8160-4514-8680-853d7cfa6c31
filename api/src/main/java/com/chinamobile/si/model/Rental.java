package com.chinamobile.si.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "rentals")
public class Rental extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    // 设备id
    @Column(length = 36, nullable = false)
    String deviceId;

    // 出租人id
    @Column(length = 36, nullable = false)
    String lessorId;

    // 承租人id
    @Column(length = 36, nullable = false)
    String lesseeId;

    @Transient
    Lessee lessee;

    Date expireTime;

    public Rental() {
    }

    public Rental(String lessorId) {
        this.lessorId = lessorId;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getLessorId() {
        return lessorId;
    }

    public void setLessorId(String lessorId) {
        this.lessorId = lessorId;
    }

    public String getLesseeId() {
        return lesseeId;
    }

    public void setLesseeId(String lesseeId) {
        this.lesseeId = lesseeId;
    }

    public Lessee getLessee() {
        return lessee;
    }

    public void setLessee(Lessee lessee) {
        this.lessee = lessee;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

}
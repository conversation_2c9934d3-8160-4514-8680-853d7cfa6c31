package com.chinamobile.si.model;

import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.util.IdWorker;
import org.springframework.util.StringUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "orders")
public class Order extends AbstractEntity {

    @Id
    @Column(length = 36)
    String id = String.valueOf(IdWorker.getInstance().nextId());

    @Column(length = 36, nullable = false)
    String platformId;

    @Column(nullable = false)
    ENUM_TYPE type;

    @Column(length = 36, nullable = false)
    String msisdn;

    @Column(nullable = false)
    String params;

    @Column(nullable = false)
    ENUM_STATUS status = ENUM_STATUS.DRAFT;

    @Column(columnDefinition = "text")
    String memo;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPlatformId() {
        return platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public ENUM_TYPE getType() {
        return type;
    }

    public void setType(ENUM_TYPE type) {
        this.type = type;
    }

    public String getMsisdn() {
        return msisdn;
    }

    public void setMsisdn(String msisdn) {
        this.msisdn = StringUtils.trimWhitespace(msisdn);
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = StringUtils.trimWhitespace(params);
    }

    public ENUM_STATUS getStatus() {
        return status;
    }

    public void setStatus(ENUM_STATUS status) {
        this.status = status;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = StringUtils.trimWhitespace(memo);
    }

    public enum ENUM_TYPE {
        BILL, PACKAGE
    }

    public enum ENUM_STATUS {
        DRAFT, PROCESSING, COMPLETED, FAILED
    }

}
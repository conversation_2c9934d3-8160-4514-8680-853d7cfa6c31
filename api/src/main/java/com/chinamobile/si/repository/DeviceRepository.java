package com.chinamobile.si.repository;

import com.chinamobile.si.model.Device;
import com.chinamobile.sparrow.domain.infra.code.ErrorCode;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.List;

@Repository
@Transactional
@ErrorCode(module = "051")
public class DeviceRepository extends AbstractEntityRepository<Device> {

    public DeviceRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Device.class);
    }

    @Transactional(readOnly = true)
    public Result<Device> get(String id) {
        Result<Device> _record = new Result<>();

        _record.data = getCurrentSession().get(Device.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Device.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Device> search(int count, int index, List<Sorter> sorters, String msisdn, String iccid, String imsi, String tenantId) {
        JinqStream<Device> _query = stream(Device.class);

        if (StringUtils.hasLength(msisdn)) {
            _query = _query.where(i -> msisdn.contains(i.getMsisdn()));
        }

        if (StringUtils.hasLength(iccid)) {
            _query = _query.where(i -> iccid.contains(i.getIccid()));
        }

        if (StringUtils.hasLength(imsi)) {
            _query = _query.where(i -> imsi.contains(i.getImsi()));
        }

        if (StringUtils.hasLength(tenantId)) {
            _query = _query.where(i -> tenantId.equals(i.getTenantId()));
        }

        if (CollectionUtils.isEmpty(sorters)) {
            _query = _query.sortedBy(Device::getIccid);
        } else {
            _query = sort(_query, sorters);
        }

        PaginatedRecords<Device> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    public Result<String> save(Device record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<Device> _record = get(record.getId());
        if (!_record.isOK()) {
            _record.data = new Device();

            _alreadyExisted = false;
        }

        if (!StringUtils.hasLength(record.getIccid()) && !StringUtils.hasLength(record.getImsi()) && !StringUtils.hasLength(record.getMsisdn())) {
            _record.setCode(Result.ENUM_ERROR.P, 1);
        }

        String _identifier = _record.data.getId();
        String _msisdn = record.getMsisdn();
        String _iccid = record.getIccid();
        String _imsi = record.getImsi();

        if (stream(Device.class).where(i -> !_identifier.equals(i.getId()) && _msisdn.equals(i.getMsisdn()))
                .findFirst().isPresent()) {
            _id.setCode(Result.ENUM_ERROR.P, 4);
            return _id;
        }

        if (StringUtils.hasLength(_iccid) && stream(Device.class).where(i -> !_identifier.equals(i.getId()) && _iccid.equals(i.getIccid()))
                .findFirst().isPresent()) {
            _id.setCode(Result.ENUM_ERROR.P, 2);
            return _id;
        }

        if (StringUtils.hasLength(_imsi) && stream(Device.class).where(i -> !_identifier.equals(i.getId()) && _imsi.equals(i.getImsi()))
                .findFirst().isPresent()) {
            _id.setCode(Result.ENUM_ERROR.P, 3);
            return _id;
        }

        ((DeviceRepository) AopContext.currentProxy()).copyProperties(record, _record.data, new String[]{"id", "tenantId"});

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

}
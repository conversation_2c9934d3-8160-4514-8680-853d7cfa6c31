package com.chinamobile.si.repository;

import com.chinamobile.si.model.Lessee;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.repository.AbstractMediaAvailableRepository;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Repository
@Transactional
public class LesseeRepository extends AbstractMediaAvailableRepository<Lessee> {

    public LesseeRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, Lessee.class);
    }

    @Override
    public List<String> getMediaIds(List<Lessee> records) {
        return CollectionUtils.isEmpty(records) ? new ArrayList<>() : records.stream()
                .map(Lessee::getAvatarId)
                .filter(StringUtils::hasLength)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Result<Lessee> get(String id) {
        Result<Lessee> _record = new Result<>();

        _record.data = getCurrentSession().get(Lessee.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Lessee.class.getSimpleName()});
        }

        return _record;
    }

    public Result<String> save(Lessee record, String operatorId) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<Lessee> _record = get(record.getId());
        if (!_record.isOK()) {
            _record.data = new Lessee();

            _alreadyExisted = false;
        }

        ((LesseeRepository) AopContext.currentProxy()).copyProperties(record, _record.data, new String[]{"id"});

        Result<Void> _success = _alreadyExisted ? update(_record.data, operatorId) : add(_record.data, operatorId);
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

}
package com.chinamobile.si.repository;

import com.chinamobile.si.model.Order;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.model.AbstractEntity;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.service.iot.RechargeFacade;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Date;

@Repository
@Transactional
public class OrderRepository extends AbstractEntityRepository<Order> {

    final RechargeFacade rechargeFacade;

    public OrderRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            RechargeFacade rechargeFacade
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Order.class);
        this.rechargeFacade = rechargeFacade;
    }

    @Transactional(readOnly = true)
    public Result<Order> get(String id) {
        Result<Order> _record = new Result<>();

        _record.data = getCurrentSession().get(Order.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Order.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Order> search(int count, int index, Order.ENUM_TYPE type, String msisdn, Date from, Date to, Order.ENUM_STATUS status, String tenantId) {
        JinqStream<Order> _query = stream(Order.class);

        if (type != null) {
            _query = _query.where(i -> type == i.getType());
        }

        if (StringUtils.hasLength(msisdn)) {
            _query = _query.where(i -> msisdn.contains(i.getMsisdn()));
        }

        if (from != null) {
            _query = _query.where(i -> !from.after(i.getCreateTime()));
        }

        if (to != null) {
            Date _to = DateUtil.addDays(to, 1);
            _query = _query.where(i -> _to.after(i.getCreateTime()));
        }

        if (status != null) {
            _query = _query.where(i -> status == i.getStatus());
        }

        if (StringUtils.hasLength(tenantId)) {
            _query = _query.where(i -> tenantId.equals(i.getCreatorId()));
        }

        _query = _query.sortedBy(AbstractEntity::getCreateTime);

        PaginatedRecords<Order> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList();

        return _page;
    }

    // 充值
    public Result<String> recharge(String msisdn, int amount, String payment, User operator) {
        Result<String> _url = new Result<>();

        Order _record = new Order();
        _record.setType(Order.ENUM_TYPE.BILL);
        _record.setMsisdn(msisdn);
        _record.setParams(String.valueOf(amount));

        // 新增
        super.add(_record, operator.getId());
        getCurrentSession().flush();

        Result<RechargeFacade.Payment> _payment = rechargeFacade.recharge(msisdn, amount, payment);
        if (_payment.isOK()) {
            _record.setPlatformId(_payment.data.getOrderNo());
            _record.setStatus(Order.ENUM_STATUS.PROCESSING);

            _url.data = _payment.data.getUrl();
        } else {
            _record.setStatus(Order.ENUM_STATUS.FAILED);
            _record.setMemo(_payment.message);

            _url.pack(_payment);
        }

        // 更新
        super.update(_record, operator.getId());
        getCurrentSession().flush();

        return _url;
    }

    // 订购流量包
    public Result<String> order(String msisdn, String packageNo, User operator) {
        Result<String> _id = new Result<>();

        Order _record = new Order();
        _record.setType(Order.ENUM_TYPE.PACKAGE);
        _record.setMsisdn(msisdn);
        _record.setParams(packageNo);

        // 新增
        super.add(_record, operator.getId());
        getCurrentSession().flush();

        Result<String> _orderNo = rechargeFacade.order(msisdn, "0", packageNo);
        if (_orderNo.isOK()) {
            _record.setPlatformId(_orderNo.data);
            _record.setStatus(Order.ENUM_STATUS.PROCESSING);

            _id.data = _orderNo.data;
        } else {
            _record.setStatus(Order.ENUM_STATUS.FAILED);
            _record.setMemo(_orderNo.message);

            _id.pack(_orderNo);
        }

        // 更新
        super.update(_record, operator.getId());
        getCurrentSession().flush();

        return _id;
    }

}
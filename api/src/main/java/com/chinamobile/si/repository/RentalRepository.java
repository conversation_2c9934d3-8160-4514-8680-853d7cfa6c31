package com.chinamobile.si.repository;

import com.chinamobile.si.model.Device;
import com.chinamobile.si.model.Lessee;
import com.chinamobile.si.model.Rental;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.lang.Sorter;
import com.chinamobile.sparrow.domain.model.sys.User;
import com.chinamobile.sparrow.domain.repository.AbstractEntityRepository;
import com.chinamobile.sparrow.domain.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.jinq.orm.stream.JinqStream;
import org.jinq.tuples.Pair;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManagerFactory;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Repository
@Transactional
public class RentalRepository extends AbstractEntityRepository<Rental> {

    public RentalRepository(
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider
    ) {
        super(entityManagerFactory, jinqJPAStreamProvider, Rental.class);
    }

    @Transactional(readOnly = true)
    public Result<Rental> get(String id) {
        Result<Rental> _record = new Result<>();

        _record.data = getCurrentSession().get(Rental.class, id);
        if (_record.data == null) {
            _record.setCode(Result.DATABASE_RECORD_NOT_FOUND, new Object[]{Rental.class.getSimpleName()});
        }

        return _record;
    }

    @Transactional(readOnly = true)
    public PaginatedRecords<Rental> search(int count, int index, String msisdn, String passport, String name, Date from, Date to, String tenantId) {
        JinqStream<Pair<Rental, Lessee>> _query = stream(Rental.class).leftOuterJoin((i, session) -> session.stream(Lessee.class), (i, j) -> j.getId().equals(i.getLesseeId()))
                .where(i -> i.getTwo() != null);

        if (StringUtils.hasLength(msisdn)) {
            _query = _query.leftOuterJoin((i, session) -> session.stream(Device.class), (i, j) -> j.getId().equals(i.getOne().getDeviceId()))
                    .where(i -> i.getTwo() != null && msisdn.contains(i.getTwo().getMsisdn()))
                    .select(Pair::getOne);
        }

        if (StringUtils.hasLength(passport)) {
            _query = _query.where(i -> passport.contains(i.getTwo().getPassport()));
        }

        if (StringUtils.hasLength(name)) {
            _query = _query.where(i -> name.contains(i.getTwo().getName()));
        }

        if (from != null) {
            _query = _query.where(i -> !from.after(i.getOne().getCreateTime()));
        }

        if (to != null) {
            Date _to = DateUtil.addDays(to, 1);
            _query = _query.where(i -> _to.after(i.getOne().getCreateTime()));
        }

        if (StringUtils.hasLength(tenantId)) {
            _query = _query.where(i -> tenantId.equals(i.getOne().getLessorId()));
        }

            _query = _query.sortedBy(i -> i.getOne().getCreateTime());

        PaginatedRecords<Rental> _page = new PaginatedRecords<>(count, index);
        _page.total = _query.count();

        if (count >= 0 && index >= 0) {
            _query = _query.skip((long) count * index).limit(count);
        }
        _page.records = _query.toList().stream()
                .map(i -> {
                    Rental _rental = i.getOne();
                    _rental.setLessee(i.getTwo());
                    return _rental;
                })
                .collect(Collectors.toList());

        return _page;
    }

    public Result<String> save(Rental record, User operator) {
        Result<String> _id = new Result<>();

        boolean _alreadyExisted = true;

        Result<Rental> _record = get(record.getId());
        if (!_record.isOK()) {
            _record.data = new Rental(operator.getId());

            _alreadyExisted = false;
        }

        ((RentalRepository) AopContext.currentProxy()).copyProperties(record, _record.data, new String[]{"id", "lessorId", "lesseeId"});

        Result<Void> _success = _alreadyExisted ? update(_record.data, operator.getId()) : add(_record.data, operator.getId());
        if (!_success.isOK()) {
            return _id.pack(_success);
        }

        _id.data = _record.data.getId();
        return _id;
    }

}
package com.chinamobile.si;

import com.chinamobile.sparrow.domain.infra.sec.shiro.ModularRealmAuthenticator;
import com.chinamobile.sparrow.domain.infra.sec.shiro.ShiroConfigurationProperties;
import com.chinamobile.sparrow.domain.infra.sec.shiro.realm.*;
import com.chinamobile.sparrow.domain.model.sys.Department;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.DefaultAccessFacade;
import org.apache.shiro.authc.AuthenticationListener;
import org.apache.shiro.authc.pam.FirstSuccessfulStrategy;
import org.apache.shiro.authz.ModularRealmAuthorizer;
import org.apache.shiro.cache.CacheManager;
import org.apache.shiro.mgt.RememberMeManager;
import org.apache.shiro.realm.Realm;
import org.apache.shiro.session.SessionListenerAdapter;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.eis.SessionDAO;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.apache.shiro.web.session.mgt.DefaultWebSessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Configuration
public class ShiroConfiguration {

    @Bean
    @ConditionalOnBean(value = AccessFacade.class)
    public WxMaCodeAuthorizingRealm wxMaCodeAuthorizingRealm(
            DefaultUserRepository userRepository,
            DepartmentRepository<Department> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            DefaultAccessFacade wxMaAccessFacade
    ) {
        WxMaCodeAuthorizingRealm _realm = new WxMaCodeAuthorizingRealm(userRepository, departmentRepository, roleRepository, permissionRepository, wxMaAccessFacade);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    @ConditionalOnBean(value = AccessFacade.class)
    public WxMaPhoneNumberAuthorizingRealm wxMaPhoneNumberAuthorizingRealm(
            DefaultUserRepository userRepository,
            DepartmentRepository<Department> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            DefaultAccessFacade wxMaAccessFacade
    ) {
        WxMaPhoneNumberAuthorizingRealm _realm = new WxMaPhoneNumberAuthorizingRealm(userRepository, departmentRepository, roleRepository, permissionRepository, wxMaAccessFacade);
        _realm.setAuthorizationCachingEnabled(false);

        return _realm;
    }

    @Bean
    public org.apache.shiro.authc.pam.ModularRealmAuthenticator authenticator(
            AuthenticationListener authenticationListener,
            ConfigUserAuthorizingRealm configUserAuthorizingRealm,
            UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm,
            SMSAuthorizingRealm smsAuthorizingRealm,
            @Autowired(required = false) CMPassportAuthorizingRealm cmPassportAuthorizingRealm,
            @Autowired(required = false) WxCpAuthorizingRealm wxCpAuthorizingRealm,
            @Autowired(required = false) WxMaCodeAuthorizingRealm wxMaCodeAuthorizingRealm,
            @Autowired(required = false) WxMaPhoneNumberAuthorizingRealm wxMaPhoneNumberAuthorizingRealm,
            @Autowired(required = false) YzyAuthorizingRealm yzyAuthorizingRealm
    ) {
        org.apache.shiro.authc.pam.ModularRealmAuthenticator _authenticator = new ModularRealmAuthenticator(configUserAuthorizingRealm, usernamePasswordAuthorizingRealm, smsAuthorizingRealm, cmPassportAuthorizingRealm, wxCpAuthorizingRealm, wxMaCodeAuthorizingRealm, wxMaPhoneNumberAuthorizingRealm, yzyAuthorizingRealm);
        _authenticator.setAuthenticationStrategy(new FirstSuccessfulStrategy());
        _authenticator.setAuthenticationListeners(Collections.singletonList(authenticationListener));

        return _authenticator;
    }

    @Bean
    public SessionManager sessionManager(
            SessionDAO sessionDAO,
            ShiroConfigurationProperties shiroConfigurationProperties,
            SessionListenerAdapter sessionListenerAdapter
    ) {
        DefaultWebSessionManager _sessionManager = new DefaultWebSessionManager();

        // 设置cookie
        _sessionManager.setSessionIdCookie(shiroConfigurationProperties.getSessionIdCookie());
        _sessionManager.setSessionIdUrlRewritingEnabled(false);

        _sessionManager.setSessionDAO(sessionDAO);

        // 设置session过期时间
        _sessionManager.setGlobalSessionTimeout(shiroConfigurationProperties.getSession().getTimeout() * 1000);
        // 设置session验证间隔
        _sessionManager.setSessionValidationInterval(shiroConfigurationProperties.getSession().getValidationInterval() * 1000);
        // 设置session监听器
        _sessionManager.setSessionListeners(Collections.singletonList(sessionListenerAdapter));

        return _sessionManager;
    }

    @Bean
    public DefaultWebSecurityManager webSecurityManager(
            ConfigUserAuthorizingRealm configUserAuthorizingRealm,
            UsernamePasswordAuthorizingRealm usernamePasswordAuthorizingRealm,
            SMSAuthorizingRealm smsAuthorizingRealm,
            @Autowired(required = false) CMPassportAuthorizingRealm cmPassportAuthorizingRealm,
            @Autowired(required = false) WxCpAuthorizingRealm wxCpAuthorizingRealm,
            @Autowired(required = false) WxMaCodeAuthorizingRealm wxMaCodeAuthorizingRealm,
            @Autowired(required = false) YzyAuthorizingRealm yzyAuthorizingRealm,
            org.apache.shiro.authc.pam.ModularRealmAuthenticator authenticator,
            ModularRealmAuthorizer authorizer,
            SessionManager sessionManager,
            CacheManager cacheManager,
            RememberMeManager rememberMeManager
    ) {
        DefaultWebSecurityManager _manager = new DefaultWebSecurityManager();
        _manager.setAuthenticator(authenticator);
        _manager.setAuthorizer(authorizer);

        // 设置认证域
        List<Realm> _realms = new ArrayList<Realm>() {{
            add(configUserAuthorizingRealm);
            add(usernamePasswordAuthorizingRealm);
            add(smsAuthorizingRealm);
        }};

        if (cmPassportAuthorizingRealm != null) {
            _realms.add(cmPassportAuthorizingRealm);
        }
        if (wxCpAuthorizingRealm != null) {
            _realms.add(wxCpAuthorizingRealm);
        }
        if (wxMaCodeAuthorizingRealm != null) {
            _realms.add(wxMaCodeAuthorizingRealm);
        }
        if (yzyAuthorizingRealm != null) {
            _realms.add(yzyAuthorizingRealm);
        }

        _manager.setRealms(_realms);

        // 设置会话管理器
        _manager.setSessionManager(sessionManager);

        // 设置缓存管理器
        _manager.setCacheManager(cacheManager);

        // 设置记住管理器
        _manager.setRememberMeManager(rememberMeManager);

        return _manager;
    }

}
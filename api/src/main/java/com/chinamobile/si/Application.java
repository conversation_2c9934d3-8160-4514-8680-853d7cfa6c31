package com.chinamobile.si;

import com.chinamobile.sparrow.domain.infra.job.QuartzJobScan;
import com.chinamobile.sparrow.domain.infra.sec.shiro.ShiroPermissionScan;
import com.chinamobile.sparrow.domain.infra.sys.PageScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.socket.config.annotation.EnableWebSocket;

@SpringBootApplication
@EnableAspectJAutoProxy(exposeProxy = true, proxyTargetClass = true)
@EnableAsync
@EnableWebMvc
@EnableWebSocket
@PageScan
@QuartzJobScan
@ShiroPermissionScan
public class Application {

    public static void main(String[] args) {
        SpringApplication _application = new SpringApplication(Application.class);
        _application.addListeners(new ApplicationPidFileWriter());
        _application.run(args);
    }

}
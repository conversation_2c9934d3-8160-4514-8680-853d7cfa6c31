package com.chinamobile.si.controller;

import com.chinamobile.si.model.Lessee;
import com.chinamobile.si.model.Rental;
import com.chinamobile.si.repository.LesseeRepository;
import com.chinamobile.si.repository.RentalRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Optional;

@RestController
@RequestMapping(value = "rental")
public class RentalController {

    final RentalRepository rentalRepository;
    final LesseeRepository lesseeRepository;
    final LoginUtil loginUtil;

    public RentalController(
            RentalRepository rentalRepository,
            LesseeRepository lesseeRepository,
            LoginUtil loginUtil
    ) {
        this.rentalRepository = rentalRepository;
        this.lesseeRepository = lesseeRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/search")
    public Result<PaginatedRecords<Rental>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _msisdn = Optional.ofNullable(data.get("msisdn"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _passport = Optional.ofNullable(data.get("passport"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        String _name = Optional.ofNullable(data.get("name"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);

        Result<PaginatedRecords<Rental>> _page = new Result<>();
        _page.data = rentalRepository.search(_count, _index, _msisdn, _passport, _name, _from, _to, null);
        return _page;

    }

    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody JsonObject data) {
        Result<String> _id = new Result<>();

        Lessee _lessee = ConverterUtil.json2Object(data.get("lessee").toString(), Lessee.class);
        Result<String> _lesseeId = lesseeRepository.save(_lessee, null);
        if (!_lesseeId.isOK()) {
            return _id.pack(_lesseeId);
        }

        Rental _record = ConverterUtil.json2Object(data.get("rental").toString(), Rental.class);
        _record.setLesseeId(_lesseeId.data);

        return rentalRepository.save(_record, loginUtil.getUser());
    }

    @PostMapping(value = "/recharge")
    public Result<?> recharge(@RequestBody JsonObject data) {
        Result<String> _id = new Result<>();

        Lessee _lessee = ConverterUtil.json2Object(data.get("lessee").toString(), Lessee.class);
        Result<String> _lesseeId = lesseeRepository.save(_lessee, null);
        if (!_lesseeId.isOK()) {
            return _id.pack(_lesseeId);
        }

        Rental _record = ConverterUtil.json2Object(data.get("rental").toString(), Rental.class);
        _record.setLesseeId(_lesseeId.data);

        return rentalRepository.save(_record, loginUtil.getUser());
    }

}
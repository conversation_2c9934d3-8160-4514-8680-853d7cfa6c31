package com.chinamobile.si.controller;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.iot.DataFacade;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping(value = "test")
public class TestController {

    final DataFacade dataFacade;

    public TestController(DataFacade dataFacade) {
        this.dataFacade = dataFacade;
    }

    @RequestMapping(value = "/data")
    public Result<?> data() {
        Result<List<DataFacade.DataAmount>> _data = new Result<>();
        _data.data = dataFacade.dailyDataUsage("iccid", Collections.singletonList("898608411924D2244196"), new Date());
        return _data;
    }

}
package com.chinamobile.si.controller;

import com.chinamobile.si.model.Order;
import com.chinamobile.si.repository.OrderRepository;
import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.lang.PaginatedRecords;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.Optional;

@RestController
@RequestMapping(value = "order")
public class OrderController {

    final OrderRepository orderRepository;
    final LoginUtil loginUtil;

    public OrderController(
            OrderRepository orderRepository,
            LoginUtil loginUtil
    ) {
        this.orderRepository = orderRepository;
        this.loginUtil = loginUtil;
    }

    @PostMapping(value = "/search")
    @RequiresPermissions(value = "order:search")
    public Result<PaginatedRecords<Order>> search(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        String _tenantId = Optional.ofNullable(data.get("tenantId"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Order.ENUM_TYPE _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : Order.ENUM_TYPE.valueOf(i.getAsString())).orElse(null);
        String _msisdn = Optional.ofNullable(data.get("msisdn"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Order.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Order.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);

        Result<PaginatedRecords<Order>> _page = new Result<>();
        _page.data = orderRepository.search(_count, _index, _type, _msisdn, _from, _to, _status, _tenantId);
        return _page;
    }

    @PostMapping(value = "/me")
    public Result<PaginatedRecords<Order>> me(@RequestBody JsonObject data) {
        int _count = data.get("count").getAsInt();
        int _index = data.get("index").getAsInt();
        Order.ENUM_TYPE _type = Optional.ofNullable(data.get("type"))
                .map(i -> i.isJsonNull() ? null : Order.ENUM_TYPE.valueOf(i.getAsString())).orElse(null);
        String _msisdn = Optional.ofNullable(data.get("msisdn"))
                .map(i -> i.isJsonNull() ? null : i.getAsString()).orElse(null);
        Date _from = Optional.ofNullable(data.get("from"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Date _to = Optional.ofNullable(data.get("to"))
                .map(i -> i.isJsonNull() ? null : DateUtil.from(i.getAsString(), "yyyy-MM-dd")).orElse(null);
        Order.ENUM_STATUS _status = Optional.ofNullable(data.get("status"))
                .map(i -> i.isJsonNull() ? null : Order.ENUM_STATUS.valueOf(i.getAsString())).orElse(null);

        Result<PaginatedRecords<Order>> _page = new Result<>();
        _page.data = orderRepository.search(_count, _index, _type, _msisdn, _from, _to, _status, loginUtil.getUserId());
        return _page;
    }

    @PostMapping(value = "/rechage")
    public Result<String> recharge(@RequestBody JsonObject data) {
        String _msisdn = data.get("msisdn").getAsString();
        int _amount = data.get("amount").getAsInt();
        String _payment = data.get("payment").getAsString();

        return orderRepository.recharge(_msisdn, _amount, _payment, loginUtil.getUser());
    }

    @PostMapping(value = "/order")
    public Result<?> order(@RequestBody JsonObject data) {
        String _msisdn = data.get("msisdn").getAsString();
        String _packageNo = data.get("packageNo").getAsString();

        return orderRepository.order(_msisdn, _packageNo, loginUtil.getUser());
    }

}
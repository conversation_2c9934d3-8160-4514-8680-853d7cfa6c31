package com.chinamobile.si;

import cn.binarywang.wx.miniapp.api.WxMaService;
import com.chinamobile.sparrow.domain.infra.sec.shiro.LoginUtil;
import com.chinamobile.sparrow.domain.repository.media.AbstractMediaRepository;
import com.chinamobile.sparrow.domain.repository.sec.PermissionRepository;
import com.chinamobile.sparrow.domain.repository.sec.RoleRepository;
import com.chinamobile.sparrow.domain.repository.sec.VerificationCodeRepository;
import com.chinamobile.sparrow.domain.repository.sys.DefaultUserRepository;
import com.chinamobile.sparrow.domain.repository.sys.DepartmentRepository;
import com.chinamobile.sparrow.domain.repository.sys.PageRepository;
import com.chinamobile.sparrow.domain.repository.sys.UserRepository;
import com.chinamobile.sparrow.domain.service.iot.DataFacade;
import com.chinamobile.sparrow.domain.service.iot.RechargeFacade;
import com.chinamobile.sparrow.domain.service.iot.TokenFacade;
import com.chinamobile.sparrow.domain.service.iot.infra.TokenStore;
import com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade;
import com.chinamobile.sparrow.domain.service.wx.ma.DefaultAccessFacade;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.DefaultRecordController;
import com.chinamobile.sparrow.springboot.web.controller.media.ReaderController;
import com.chinamobile.sparrow.springboot.web.controller.media.RecordController;
import com.chinamobile.sparrow.springboot.web.controller.sec.DefaultLoginController;
import com.chinamobile.sparrow.springboot.web.controller.sec.LoginController;
import com.chinamobile.sparrow.springboot.web.controller.sys.*;
import okhttp3.ConnectionPool;
import org.jinq.jpa.JinqJPAStreamProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.persistence.EntityManagerFactory;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class BeanConfiguration {

    @Bean(value = "wxMaAccessFacade")
    @ConditionalOnProperty(value = "wx.ma.enabled", havingValue = "true")
    public AccessFacade accessFacade(WxMaService wxMaService) {
        return new DefaultAccessFacade(wxMaService);
    }

    @Bean
    public TokenStore tokenStore(
            @Value(value = "${iot.base-url}") String baseUrl,
            @Value(value = "${iot.app-id}") String appId,
            @Value(value = "${iot.password}") String password,
            ConnectionPool connectionPool
    ) {
        return new TokenStore(new TokenFacade(baseUrl, appId, password, connectionPool));
    }

    @Bean
    public DataFacade dataFacade(
            @Value(value = "${iot.base-url}") String baseUrl,
            @Value(value = "${iot.app-id}") String appId,
            ConnectionPool connectionPool,
            TokenStore tokenStore,
            ThreadPoolExecutor executor
    ) {
        return new DataFacade(baseUrl, appId, connectionPool, tokenStore, executor);
    }

    @Bean
    public RechargeFacade rechargeFacade(
            @Value(value = "${iot.base-url}") String baseUrl,
            @Value(value = "${iot.app-id}") String appId,
            ConnectionPool connectionPool,
            TokenStore tokenStore,
            ThreadPoolExecutor executor
    ) {
        return new RechargeFacade(baseUrl, appId, connectionPool, tokenStore, executor);
    }

    @Bean
    public UserRepository<?> userRepository(
            @Value(value = "${sec.password-constraint}") String passwordConstraint,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            @Qualifier(value = "mainSessionFactory") EntityManagerFactory entityManagerFactory,
            @Qualifier(value = "mainJinqJPAStreamProvider") JinqJPAStreamProvider jinqJPAStreamProvider,
            AbstractMediaRepository mediaRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade accessFacade
    ) {
        return new DefaultUserRepository(entityManagerFactory, jinqJPAStreamProvider, mediaRepository, passwordConstraint, rsaPrivateKey, accessFacade);
    }

    @Bean
    public LoginController loginController(
            @Value(value = "${sec.captcha}") boolean captcha,
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            @Value(value = "${sec.rsa.default.private-key}") String rsaPrivateKey,
            VerificationCodeRepository verificationCodeRepository,
            com.chinamobile.sparrow.domain.service.wx.ma.AccessFacade wxMaAccessFacade,
            LoginUtil loginUtil
    ) {
        return new DefaultLoginController(captcha, rsaPublicKey, rsaPrivateKey, verificationCodeRepository, null, null, null, wxMaAccessFacade, loginUtil);
    }

    @Bean
    public ProfileController profileController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultProfileController(userRepository, loginUtil);
    }

    @Bean
    public SettingController settingController(
            @Value(value = "${sec.rsa.default.public-key}") String rsaPublicKey,
            DefaultUserRepository userRepository,
            DepartmentRepository<?> departmentRepository,
            RoleRepository roleRepository,
            PermissionRepository permissionRepository,
            PageRepository pageRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultSettingController(rsaPublicKey, userRepository, departmentRepository, roleRepository, permissionRepository, pageRepository, loginUtil);
    }

    @Bean
    public ReaderController readerController(
            AbstractMediaRepository mediaRepository,
            RecordController recordController,
            LoginUtil loginUtil
    ) {
        return new DefaultReaderController(mediaRepository, recordController, loginUtil);
    }

    @Bean
    public RecordController recordController(
            AbstractMediaRepository mediaRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultRecordController(mediaRepository, loginUtil);
    }

    @Bean
    public UserController userController(
            DefaultUserRepository userRepository,
            LoginUtil loginUtil
    ) {
        return new DefaultUserController(userRepository, loginUtil);
    }

}
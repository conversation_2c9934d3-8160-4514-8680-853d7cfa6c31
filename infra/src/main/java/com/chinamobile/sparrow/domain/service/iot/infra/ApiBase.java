package com.chinamobile.sparrow.domain.service.iot.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.gson.JsonObject;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.security.SecureRandom;
import java.util.Date;
import java.util.function.Function;

public abstract class ApiBase {

    protected static String STATUS_OK = "0";

    protected final String baseUrl;
    protected final String appId;
    protected OkHttpClient client;

    protected ApiBase(String baseUrl, String appId) {
        this.baseUrl = baseUrl;
        this.appId = appId;
    }

    protected <T> Result<T> invoke(Function<String, T> function, Request.Builder builder) {
        Result<T> _result = new Result<>();

        String _transId = appId + DateUtil.toString(new Date(), "yyyyMMddHHmmss") + new SecureRandom().nextInt(100000000);

        HttpUrl _url = builder.getUrl$okhttp().newBuilder()
                .addQueryParameter("transid", _transId)
                .build();

        try (Response response = client.newCall(builder.url(_url).build()).execute()) {
            if (response.isSuccessful()) {
                JsonObject _json = ConverterUtil.json2Object(response.body().string(), JsonObject.class);

                String _status = _json.get("status").getAsString();
                if (!STATUS_OK.equals(_status)) {
                    _result.setCode(_status);
                    _result.message = _json.get("message").getAsString();
                    return _result;
                }

                _result.data = function.apply(_json.get("result").toString());
            } else {
                _result.setCode(Result.ENUM_ERROR.N, response.code());
                _result.message = response.message();
            }
        } catch (Throwable e) {
            _result.setCode(Result.SERVICE_UNKNOWN);
            _result.message = e.getMessage();
        }

        return _result;
    }

}
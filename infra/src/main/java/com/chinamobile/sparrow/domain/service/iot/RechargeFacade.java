package com.chinamobile.sparrow.domain.service.iot;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.iot.infra.AuthenticatedApiBase;
import com.chinamobile.sparrow.domain.service.iot.infra.TokenStore;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.google.gson.JsonArray;
import okhttp3.ConnectionPool;
import okhttp3.Request;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ThreadPoolExecutor;

public class RechargeFacade extends AuthenticatedApiBase {

    final ThreadPoolExecutor executor;
    final Logger logger;

    public RechargeFacade(
            String baseUrl,
            String appId,
            ConnectionPool connectionPool,
            TokenStore tokenStore,
            ThreadPoolExecutor executor
    ) {
        super(baseUrl, appId, connectionPool, tokenStore);
        this.executor = executor;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public Result<Payment> recharge(String msisdn, int amount, String payment) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/ec/recharge/sim-account?entityType=msisdn&entityId=" + msisdn + "&chargeMoney=" + amount + "&paymentType=" + payment + "&returnUrl=")
                .get();

        return invoke(json -> {
            JsonArray _array = ConverterUtil.json2Object(json, JsonArray.class);
            return ConverterUtil.json2Object(_array.get(0).toString(), Payment.class);
        }, _builder);
    }

    /**
     * 订购流量包
     *
     * @param msisdn
     * @param product 0为物联网卡，1为车联网卡
     * @param packageNo 加油包套餐： 0:流量加油包 10元套餐（物联卡，100M） 1:流量加油包10元套餐（车联网，100M） 2:流量加油包 30元套餐（物联卡，500M） 3:流量加油包30元套餐（车联网，500M） 4:20版流量加油包 8元套餐（物联卡） 5:20版流量加油包8元套餐（车联网） 6:20版流量加油包 20元套餐（物联卡） 7:20版流量加油包20元套餐（车联网）
     * @return
     */
    public Result<String> order(String msisdn, String product, String packageNo) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/ec/order/gprspackage-order?msisdn=" + msisdn + "&maincommoDity=" + product + "&packageType=" + packageNo)
                .get();

        return invoke(json -> {
            JsonArray _array = ConverterUtil.json2Object(json, JsonArray.class);
            return _array.get(0).getAsJsonObject().get("orderNum").getAsString();
        }, _builder);
    }

    public static class Payment {

        String orderNo;
        String url;

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getUrl() {
            return url;
        }

        public void setUrl(String url) {
            this.url = url;
        }

    }

}
package com.chinamobile.sparrow.domain.service.iot;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.iot.infra.ApiBase;
import com.chinamobile.sparrow.domain.service.iot.lang.Token;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.chinamobile.sparrow.domain.util.HttpUtil;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import okhttp3.OkHttpClient;
import okhttp3.Request;

import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class TokenFacade extends ApiBase {

    protected final String password;

    public TokenFacade(String baseUrl, String appId, String password, ConnectionPool connectionPool) {
        super(baseUrl, appId);
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .build();
        this.password = password;
    }

    public Result<Token> get() {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/ec/get/token?appid=" + appId + "&password=" + password + "&refresh=0")
                .get();

        return invoke(json -> {
            JsonArray _array = ConverterUtil.json2Object(json, JsonArray.class);
            JsonObject _json = _array.get(0).getAsJsonObject();

            int _ttl = Integer.parseInt(_json.get("ttl").getAsString());

            Token _token = new Token();
            _token.setValue(_json.get("token").getAsString());
            _token.setExp(DateUtil.addSeconds(new Date(), _ttl));
            return _token;
        }, _builder);
    }

}
package com.chinamobile.sparrow.domain.service.iot.infra;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.iot.TokenFacade;
import com.chinamobile.sparrow.domain.service.iot.lang.Token;

import java.util.Date;

public class TokenStore {

    Token token = null;

    final TokenFacade tokenFacade;

    public TokenStore(TokenFacade tokenFacade) {
        this.tokenFacade = tokenFacade;
    }

    public String getToken() {
        return token == null || new Date().after(token.getExp()) ? refreshToken() : token.getValue();
    }

    public String refreshToken() {
        Result<Token> _token = tokenFacade.get();
        if (_token.isOK()) {
            token = _token.data;
        }

        return token.getValue();
    }

}
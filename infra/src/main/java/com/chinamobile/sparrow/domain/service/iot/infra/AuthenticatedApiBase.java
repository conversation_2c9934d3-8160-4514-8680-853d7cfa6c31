package com.chinamobile.sparrow.domain.service.iot.infra;

import com.chinamobile.sparrow.domain.util.HttpUtil;
import okhttp3.ConnectionPool;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;

import java.util.concurrent.TimeUnit;

import static com.chinamobile.sparrow.domain.util.HttpUtil.createUnsafeSSLContext;

public class AuthenticatedApiBase extends ApiBase {

    protected final TokenStore tokenStore;

    public AuthenticatedApiBase(String baseUrl, String appId, ConnectionPool connectionPool, TokenStore tokenStore) {
        super(baseUrl, appId);

        this.tokenStore = tokenStore;
        this.client = new OkHttpClient.Builder()
                .connectionPool(connectionPool == null ? new ConnectionPool() : connectionPool)
                .readTimeout(10, TimeUnit.MINUTES)
                .sslSocketFactory(createUnsafeSSLContext().getSocketFactory(), new HttpUtil.TrustAllCertsManager())
                .hostnameVerifier(new HttpUtil.TrustAllHostnameVerifier())
                .addInterceptor(chain -> {
                    String _token = tokenStore.getToken();

                    HttpUrl _url = chain.request().url().newBuilder()
                            .addQueryParameter("token", _token)
                            .build();

                    Request.Builder _builder = chain.request().newBuilder()
                            .url(_url);
                    return chain.proceed(_builder.build());
                })
                .build();
    }

}
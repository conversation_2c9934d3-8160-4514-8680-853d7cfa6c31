package com.chinamobile.sparrow.domain.service.iot;

import com.chinamobile.sparrow.domain.infra.code.Result;
import com.chinamobile.sparrow.domain.service.iot.infra.AuthenticatedApiBase;
import com.chinamobile.sparrow.domain.service.iot.infra.TokenStore;
import com.chinamobile.sparrow.domain.util.ConverterUtil;
import com.chinamobile.sparrow.domain.util.DateUtil;
import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import okhttp3.ConnectionPool;
import okhttp3.Request;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletionService;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

public class DataFacade extends AuthenticatedApiBase {

    final ThreadPoolExecutor executor;
    final Logger logger;

    public DataFacade(
            String baseUrl,
            String appId,
            ConnectionPool connectionPool,
            TokenStore tokenStore,
            ThreadPoolExecutor executor
    ) {
        super(baseUrl, appId, connectionPool, tokenStore);
        this.executor = executor;
        this.logger = LoggerFactory.getLogger(this.getClass());
    }

    public List<DataAmount> dailyDataUsage(String type, List<String> ids, Date date) {
        CompletionService<List<DataAmount>> _completion = new ExecutorCompletionService<>(executor);

        List<Future<List<DataAmount>>> futures = new ArrayList<>();

        for (int i = 0; i < ids.size(); i += 100) {
            // 分组
            List<String> _ids = ids.subList(i, Math.min(i + 100, ids.size()));
            String _temp = String.join("_", _ids);

            Request.Builder _builder = new Request.Builder()
                    .url(baseUrl + "/ec/query/sim-data-usage-daily/batch?" + type + "s=" + _temp + "&queryDate=" + DateUtil.toString(date, "yyyyMMdd"))
                    .get();

            futures.add(_completion.submit(() -> {
                Result<List<DataAmount>> _one = invoke(json -> {
                    List<DataAmount> _amounts = new ArrayList<>();

                    JsonArray _array = ConverterUtil.json2Object(json, JsonArray.class);
                    for (int j = _array.size(); j >= 0; j--) {
                        Date _date = DateUtil.addDays(date, -j);

                        List<DataAmountDTO> _dtos = ConverterUtil.json2Object(_array.get(j).getAsJsonObject().get("dataAmountList").toString(), new TypeToken<DataAmount>() {
                        }.getType());
                        _amounts.addAll(_dtos.stream()
                                .map(k -> new DataAmount(k, _date))
                                .collect(Collectors.toList()));
                    }

                    return _amounts;
                }, _builder);

                if (_one.isOK()) {
                    return _one.data;
                } else {
                    logger.error("物联卡单日GPRS流量使用量批量查询失败（{}）", _one.message);

                    return new ArrayList<>();
                }
            }));
        }

        List<DataAmount> _amounts = new ArrayList<>();
        for (Future<List<DataAmount>> future : futures) {
            try {
                _amounts.addAll(future.get());
            } catch (Exception e) {
                logger.error("物联卡单日GPRS流量使用量批量查询失败", e);
            }
        }

        return _amounts;
    }

    public List<DataAmount> monthlyDataUsage(String type, List<String> ids, Date date) {
        CompletionService<List<DataAmount>> _completion = new ExecutorCompletionService<>(executor);

        List<Future<List<DataAmount>>> futures = new ArrayList<>();

        for (int i = 0; i < ids.size(); i += 100) {
            // 分组
            List<String> _ids = ids.subList(i, Math.min(i + 100, ids.size()));
            String _temp = String.join("_", _ids);

            Request.Builder _builder = new Request.Builder()
                    .url(baseUrl + "/ec/query/sim-data-usage-monthly/batch?" + type + "s=" + _temp + "&queryDate=" + DateUtil.toString(date, "yyyyMM"))
                    .get();

            futures.add(_completion.submit(() -> {
                Result<List<DataAmount>> _one = invoke(json -> {
                    List<DataAmount> _amounts = new ArrayList<>();

                    JsonArray _array = ConverterUtil.json2Object(json, JsonArray.class);
                    for (int j = 0; j < _array.size(); j++) {
                        JsonObject _json = _array.get(j).getAsJsonObject();

                        Date _date = DateUtil.from(_json.get("deadLine").getAsString(), "yyyyMMdd");

                        List<DataAmountDTO> _dtos = ConverterUtil.json2Object(_json.get("dataAmountList").toString(), new TypeToken<DataAmount>() {
                        }.getType());
                        _amounts.addAll(_dtos.stream()
                                .map(k -> new DataAmount(k, _date))
                                .collect(Collectors.toList()));
                    }

                    return _amounts;
                }, _builder);

                if (_one.isOK()) {
                    return _one.data;
                } else {
                    logger.error("物联卡单月GPRS流量使用量批量查询失败（{}）", _one.message);

                    return new ArrayList<>();
                }
            }));
        }

        List<DataAmount> _amounts = new ArrayList<>();
        for (Future<List<DataAmount>> future : futures) {
            try {
                _amounts.addAll(future.get());
            } catch (Exception e) {
                logger.error("物联卡单月GPRS流量使用量批量查询失败", e);
            }
        }

        return _amounts;
    }

    public Result<BigDecimal> groupDataUsage(String groupId) {
        Request.Builder _builder = new Request.Builder()
                .url(baseUrl + "/ec/query/group-data-usage?groupId=" + groupId)
                .get();

        return invoke(json -> {
            JsonArray _array = ConverterUtil.json2Object(json, JsonArray.class);
            return _array.get(0).getAsJsonObject().get("useAmount").getAsBigDecimal();
        }, _builder);
    }

    static class DataAmountDTO {

        String iccid;
        String imsi;
        String msisdn;
        String dataAmount;

        public String getIccid() {
            return iccid;
        }

        public void setIccid(String iccid) {
            this.iccid = iccid;
        }

        public String getImsi() {
            return imsi;
        }

        public void setImsi(String imsi) {
            this.imsi = imsi;
        }

        public String getMsisdn() {
            return msisdn;
        }

        public void setMsisdn(String msisdn) {
            this.msisdn = msisdn;
        }

        public String getDataAmount() {
            return dataAmount;
        }

        public void setDataAmount(String dataAmount) {
            this.dataAmount = dataAmount;
        }

    }

    public static class DataAmount {

        String iccid;
        String imsi;
        String msisdn;
        Date date;
        Long dataAmount;

        public DataAmount() {
        }

        DataAmount(DataAmountDTO dto, Date date) {
            this.iccid = dto.getIccid();
            this.imsi = dto.getImsi();
            this.msisdn = dto.getMsisdn();
            this.date = date;
            this.dataAmount = StringUtils.hasLength(dto.getDataAmount()) ? Long.parseLong(dto.getDataAmount()) : 0L;
        }

        public String getIccid() {
            return iccid;
        }

        public void setIccid(String iccid) {
            this.iccid = iccid;
        }

        public String getImsi() {
            return imsi;
        }

        public void setImsi(String imsi) {
            this.imsi = imsi;
        }

        public String getMsisdn() {
            return msisdn;
        }

        public void setMsisdn(String msisdn) {
            this.msisdn = msisdn;
        }

        public Date getDate() {
            return date;
        }

        public void setDate(Date date) {
            this.date = date;
        }

        public Long getDataAmount() {
            return dataAmount;
        }

        public void setDataAmount(Long dataAmount) {
            this.dataAmount = dataAmount;
        }

    }

}